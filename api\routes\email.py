from fastapi import APIRouter, HTTPException, Depends, Request
import sqlalchemy
import hashlib
import logging
from datetime import datetime, timezone, timedelta
import asyncio
from fastapi.responses import HTMLResponse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import (
        users,
        email_history,
        email_creation_logs,
        email_sending_logs,
        EmailVerifyRequest,
        EmailSendRequest,
    )
    from api.db import get_db, with_db_retry, rate_limit
    from api.auth import get_current_user, get_client_ip
    from api.utils.common import get_request_host
    from api.utils.email import (
        get_emails_async,
        create_email_account,
        get_emails,
        send_email_async,
        get_html_preview,
        cleanup_expired_html_records,
    )
    from api.utils.billing import record_consumption
except:
    from models import (
        users,
        email_history,
        email_creation_logs,
        email_sending_logs,
        EmailVerifyRequest,
        EmailSendRequest,
    )
    from db import get_db, with_db_retry, rate_limit
    from auth import get_current_user, get_client_ip
    from utils.common import get_request_host
    from utils.email import (
        get_emails_async,
        create_email_account,
        get_emails,
        send_email_async,
        get_html_preview,
        cleanup_expired_html_records,
    )
    from utils.billing import record_consumption

router = APIRouter(prefix="/api/v1", tags=["email"])
html_preview_router = APIRouter(tags=["email"])


# 获取HTML预览路由
def get_html_preview_router():
    """返回HTML预览路由，供main.py使用"""
    return html_preview_router


# 使用models中定义的EmailSendRequest模型


@router.post("/email")
async def verify_email(
    request: EmailVerifyRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    client_ip = get_client_ip(req) if req else "unknown"
    request_host = get_request_host(req) if req else None

    with rate_limit(f"email:{client_ip}", max_requests=30, expire=5):
        # 获取邮件的逻辑
        @with_db_retry(max_retries=3)
        async def get_emails_with_billing():
            with get_db() as db:
                # 获取邮件内容 - 尝试使用异步方法
                try:
                    # 使用异步方法获取邮件
                    result = await get_emails_async(
                        request.email,
                        request.num,
                        current_user["username"],
                        request_host,
                    )
                except Exception as e:
                    logger.warning(
                        f"邮件获取：异步邮件获取失败，回退到同步方法: {str(e)}"
                    )
                    result = get_emails(
                        request.email,
                        request.num,
                        current_user["username"],
                        request_host,
                    )

                status = bool(result)

                if result == {"msg": "未注册"}:
                    return create_email_account_handler(request, current_user)

                if not result or not isinstance(result, list) or len(result) == 0:
                    response = {
                        "code": 200,
                        "status": status,
                        "data": {"email": request.email, "result": result},
                    }

                    return response

                # 为每封邮件添加格式化时间
                for email in result:
                    if "Time" in email and email["Time"]:
                        try:
                            # 确保时间戳是整数类型
                            timestamp = int(email["Time"])
                            # 使用datetime直接处理，转换为UTC+8时区
                            utc8 = timezone(
                                timezone.utc.utcoffset(None) + timedelta(hours=8)
                            )
                            email_time = datetime.fromtimestamp(timestamp, tz=utc8)
                            email["FormatTime"] = email_time.strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                        except ValueError as e:
                            logger.error(
                                f"时间转换：时间戳值错误: {email['Time']}, 错误: {str(e)}"
                            )
                            email["FormatTime"] = "时间格式错误"
                        except OverflowError as e:
                            logger.error(
                                f"时间转换：时间戳溢出: {email['Time']}, 错误: {str(e)}"
                            )
                            # 尝试将时间戳视为毫秒级
                            try:
                                timestamp = int(email["Time"]) / 1000
                                utc8 = timezone(
                                    timezone.utc.utcoffset(None) + timedelta(hours=8)
                                )
                                email_time = datetime.fromtimestamp(timestamp, tz=utc8)
                                email["FormatTime"] = email_time.strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                )
                            except Exception:
                                email["FormatTime"] = "时间戳溢出"
                        except Exception as e:
                            logger.error(
                                f"时间转换：时间戳转换错误: {email['Time']}, 错误: {str(e)}"
                            )
                            email["FormatTime"] = "时间格式错误"

                # 查询用户之前接收的邮件内容哈希 - 使用优化查询
                with db.begin():
                    # 使用索引加速查询
                    stmt = sqlalchemy.select(email_history.c.content_hash).where(
                        email_history.c.username == current_user["username"]
                    )
                    query_result = db.execute(stmt)
                    existing_hashes = {row[0] for row in query_result}

                    # 批量收集需要插入的数据而不是逐条插入
                    new_emails = []
                    insert_values = []

                    for email in result:
                        content = email.get("Content", "")
                        content_hash = hashlib.md5(content.encode()).hexdigest()

                        # 检查是否为新内容
                        if content_hash not in existing_hashes:
                            new_emails.append(email)
                            # 收集批量插入数据
                            # 使用UTC+8时区保存时间
                            utc8 = timezone(
                                timezone.utc.utcoffset(None) + timedelta(hours=8)
                            )
                            insert_values.append(
                                {
                                    "username": current_user["username"],
                                    "email": request.email,
                                    "content_hash": content_hash,
                                    "received_at": datetime.now(tz=utc8),
                                }
                            )

                    # 批量插入，减少数据库操作次数
                    if insert_values:
                        db.execute(email_history.insert(), insert_values)

                    # 计算扣费金额
                    new_email_count = len(new_emails)
                    fee = 10 * new_email_count if new_email_count > 0 else 0

                    # 如果有新邮件且需要扣费
                    if fee > 0:
                        # 检查余额是否足够
                        if current_user["balance"] < fee:
                            raise HTTPException(
                                status_code=400, detail=f"余额不足，需要{fee}点余额"
                            )

                        # 扣除余额
                        new_balance = current_user["balance"] - fee
                        update_stmt = (
                            users.update()
                            .where(users.c.username == current_user["username"])
                            .values(balance=new_balance)
                        )
                        db.execute(update_stmt)

                        # 记录消费
                        try:
                            # 使用异步函数但同步调用，避免阻塞主流程
                            asyncio.create_task(
                                record_consumption(
                                    username=current_user["username"],
                                    details=f"获取邮箱 {request.email}，获取{new_email_count}封新邮件",
                                    amount=fee,
                                )
                            )
                        except Exception as e:
                            logger.error(f"记录消费失败: {str(e)}")
                            # 继续执行，不因记录失败而中断流程

                        response = {
                            "code": 200,
                            "status": True,
                            "data": {
                                "email": request.email,
                                "result": result,
                                "new_emails": new_email_count,
                                "fee": fee,
                                "balance": new_balance,
                                "message": f"获取到{new_email_count}封新邮件，扣除{fee}点余额",
                            },
                        }
                        return response
                    else:
                        # 没有新邮件，不扣费
                        response = {
                            "code": 200,
                            "status": True,
                            "data": {
                                "email": request.email,
                                "result": result,
                                "new_emails": 0,
                                "fee": 0,
                                "balance": current_user["balance"],
                                "message": "没有新邮件，不扣费",
                            },
                        }
                        return response

        return await get_emails_with_billing()


# 提取创建邮箱账户的处理逻辑为单独函数
def create_email_account_handler(request, current_user):
    @with_db_retry(max_retries=3)
    def create_email():
        with get_db() as db:
            with db.begin():
                # 直接使用 current_user 的余额信息
                price = 10
                if current_user["balance"] < price:
                    raise HTTPException(status_code=400, detail="余额不足")
                logger.info(
                    f"邮箱注册：用户 {current_user['username']} 注册邮箱 {request.email}"
                )
                result = create_email_account(request.email, current_user["username"])
                # 更灵活的成功判断逻辑
                status = (
                    result["message"] == "邮箱账号创建成功"
                    or "邮箱服务暂时不可用，但账户已记录" in result["message"]
                    or "already exist" in result["message"]
                )

                if status:
                    # 只有在创建成功时扣费
                    new_balance = current_user["balance"] - price
                    stmt = (
                        users.update()
                        .where(users.c.username == current_user["username"])
                        .values(balance=new_balance)
                    )
                    update_result = db.execute(stmt)

                    if update_result.rowcount == 0:
                        raise sqlalchemy.exc.OperationalError("更新失败，请重试")

                    # 添加邮箱创建日志记录 - 移除created_at_ts和expires_at_ts字段
                    db.execute(
                        email_creation_logs.insert().values(
                            username=current_user["username"],
                            email=request.email,
                            created_at=datetime.now(timezone.utc),
                            expires_at=datetime.now(timezone.utc) + timedelta(days=30),
                        )
                    )

                    # 记录消费
                    try:
                        # 使用异步函数但同步调用，避免阻塞主流程
                        asyncio.create_task(
                            record_consumption(
                                username=current_user["username"],
                                details=f"创建邮箱 {request.email}",
                                amount=price,
                            )
                        )
                    except Exception as e:
                        logger.error(f"记录消费失败: {str(e)}")
                        # 继续执行，不因记录失败而中断流程

                    return {
                        "code": 200,
                        "status": status,
                        "data": {
                            "email": request.email,
                            "result": result["message"],
                            "balance": new_balance,
                            "message": f"邮箱创建成功，已扣除 {price} 余额",
                        },
                    }
                else:
                    if "already exist" in result["message"]:
                        return {
                            "code": 200,
                            "status": status,
                            "data": {
                                "email": request.email,
                                "result": result["message"],
                                "balance": current_user["balance"],
                                "message": "邮箱已被他人注册",
                            },
                        }
                    return {
                        "code": 200,
                        "status": status,
                        "data": {
                            "email": request.email,
                            "result": result["message"],
                            "balance": current_user["balance"],
                            "message": "邮箱创建失败",
                        },
                    }

    return create_email()


@router.post("/send-email")
async def send_email_route(
    request: EmailSendRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """发送邮件接口"""
    client_ip = get_client_ip(req) if req else "unknown"

    # 验证邮件地址格式
    if "@" not in request.to_email:
        raise HTTPException(status_code=400, detail="目标邮箱地址格式不正确")

    with rate_limit(f"send_email:{client_ip}", max_requests=10, expire=60):

        @with_db_retry(max_retries=3)
        async def send_email_with_billing():
            with get_db() as db:
                with db.begin():
                    # 发送邮件费用
                    price = 500

                    # 检查余额是否足够
                    if current_user["balance"] < price:
                        raise HTTPException(
                            status_code=400, detail="余额不足，发送邮件需要5点余额"
                        )

                    logger.info(
                        f"发送邮箱：用户 {current_user['username']} 发送邮件 从 {request.from_email} 到 {request.to_email}"
                    )

                    # 尝试使用异步方法发送邮件
                    result = await send_email_async(
                        request.from_email,
                        request.to_email,
                        request.subject,
                        request.content,
                        current_user["username"],
                    )

                    if not result["status"]:
                        return {
                            "code": 400,
                            "status": False,
                            "data": {"message": result["message"]},
                        }

                    # 使用UTC+8时区记录时间
                    utc8 = timezone(timezone.utc.utcoffset(None) + timedelta(hours=8))
                    current_time = datetime.now(tz=utc8)

                    # 扣减用户余额
                    new_balance = current_user["balance"] - price
                    stmt = (
                        users.update()
                        .where(users.c.username == current_user["username"])
                        .values(balance=new_balance)
                    )
                    update_result = db.execute(stmt)

                    if update_result.rowcount == 0:
                        raise sqlalchemy.exc.OperationalError("更新余额失败，请重试")

                    # 记录发送日志
                    db.execute(
                        email_sending_logs.insert().values(
                            username=current_user["username"],
                            from_email=f"{request.from_email}@kedaya.xyz",
                            to_email=request.to_email,
                            subject=request.subject,
                            content_hash=hashlib.md5(
                                request.content.encode()
                            ).hexdigest(),
                            sent_at=current_time,
                            status=True,
                            sent_at_ts=int(current_time.timestamp()),
                        )
                    )

                    # 记录消费
                    try:
                        # 使用异步函数但同步调用，避免阻塞主流程
                        asyncio.create_task(
                            record_consumption(
                                username=current_user["username"],
                                details=f"发送邮箱 从 {request.from_email}@kedaya.xyz 到 {request.to_email}",
                                amount=price,
                            )
                        )
                    except Exception as e:
                        logger.error(f"记录消费失败: {str(e)}")
                        # 继续执行，不因记录失败而中断流程

                    return {
                        "code": 200,
                        "status": True,
                        "data": {
                            "message": "邮件发送成功",
                            "balance": new_balance,
                            "fee": price,
                        },
                    }

        return await send_email_with_billing()


# 定义HTML预览路由
@html_preview_router.get("/api/email/preview/{html_id}", response_class=HTMLResponse)
async def preview_email_html(html_id: str):
    """预览邮件HTML内容

    参数:
    html_id: HTML文件的唯一标识符
    """
    # 启动一次清理过期记录的过程
    try:
        asyncio.create_task(asyncio.to_thread(cleanup_expired_html_records))
    except Exception:
        # 忽略清理错误，不影响主流程
        pass

    # 获取HTML内容
    result = get_html_preview(html_id)

    if not result["status"]:
        raise HTTPException(status_code=404, detail=result["message"])

    # 返回HTML响应
    return result["html"]
