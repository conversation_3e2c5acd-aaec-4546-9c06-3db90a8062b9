import imaplib
import ssl
from email.parser import Parser
from email.header import decode_header
from email.utils import parseaddr, parsedate_to_datetime, formataddr
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from concurrent.futures import ThreadPoolExecutor
import asyncio
import re
import logging
import smtplib
import uuid
import time
import hashlib
import threading
from typing import Dict, Optional
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.utils.api_client import get_dynamic_domains
    from api.models import temp_html_storage
    from api.db import get_db
except:
    from utils.api_client import get_dynamic_domains
    from models import temp_html_storage
    from db import get_db


# 创建线程池用于处理IO密集型任务
email_thread_pool = ThreadPoolExecutor(max_workers=10)

# 临时链接有效期（秒）
TEMP_LINK_EXPIRY = 600  # 10分钟


# 使用数据库存储临时HTML内容和元数据
def save_html_content(html_content, email_id, username=None):
    try:
        # 计算内容哈希值
        content_hash = hashlib.md5(html_content.encode()).hexdigest()

        current_time = int(time.time())
        expires_at = current_time + TEMP_LINK_EXPIRY

        with get_db() as db:
            try:
                # 检查是否存在相同哈希值的记录
                existing_record = db.execute(
                    temp_html_storage.select().where(
                        temp_html_storage.c.content_hash == content_hash
                    )
                ).fetchone()

                if existing_record:
                    # 更新过期时间
                    db.execute(
                        temp_html_storage.update()
                        .where(temp_html_storage.c.id == existing_record.id)
                        .values(expires_at=expires_at)
                    )
                    db.commit()
                    return existing_record.id
                else:
                    # 创建唯一的ID
                    unique_id = str(uuid.uuid4())

                    # 存储到数据库
                    db.execute(
                        temp_html_storage.insert().values(
                            id=unique_id,
                            email_id=email_id,
                            username=username,
                            content_hash=content_hash,
                            html_content=html_content,
                            created_at=current_time,
                            expires_at=expires_at,
                        )
                    )
                    db.commit()
                    return unique_id
            except Exception as e:
                # 如果出现字段不存在的错误，尝试使用更简单的插入方式
                if "does not exist" in str(e):
                    logger.warning(f"字段不存在错误，使用基本字段插入: {str(e)}")
                    unique_id = str(uuid.uuid4())

                    # 只使用确定存在的字段
                    insert_values = {
                        "id": unique_id,
                        "email_id": email_id,
                        "html_content": html_content,
                        "created_at": current_time,
                        "expires_at": expires_at,
                    }

                    # 尝试添加content_hash字段（如果存在模型中）
                    try:
                        insert_values["content_hash"] = content_hash
                    except:
                        pass

                    db.execute(temp_html_storage.insert().values(**insert_values))
                    db.commit()
                    logger.info(f"使用基本字段创建了HTML记录: {unique_id}")
                    return unique_id
                else:
                    raise
    except Exception as e:
        logger.error(f"保存HTML内容到数据库时出错: {str(e)}")
        return None


def decode_str(s):
    value, charset = decode_header(s)[0]
    return value.decode(charset) if charset else value


def guess_charset(msg):
    charset = msg.get_charset()
    if charset is None:
        content_type = msg.get("Content-Type", "").lower()
        pos = content_type.find("charset=")
        if pos >= 0:
            charset = content_type[pos + 8 :].strip()
    return charset


def extract_info(msg, mail_id=None, username=None, request_host=None):
    """提取邮件信息

    Args:
        msg: 邮件消息对象
        mail_id: 邮件ID
        username: 用户名
        request_host: 请求的host信息，用于生成HTML预览链接
    """
    result = {}
    for header in ["From", "To", "Subject", "Date"]:
        value = msg.get(header, "")
        if value:
            if header == "Subject":
                value = decode_str(value)
            elif header == "Date":
                date_time = parsedate_to_datetime(value)
                value = int(date_time.timestamp())
                result["Time"] = value
                # 添加格式化的时间
                result["FormatTime"] = date_time.strftime("%Y-%m-%d %H:%M:%S")
                continue
            else:
                hdr, addr = parseaddr(value)
                name = decode_str(hdr)
                value = f"{name} <{addr}>"
            result[header] = value

    if msg.is_multipart():
        for part in msg.get_payload():
            part_info = extract_info(part, mail_id, username, request_host)
            # 合并部分信息，但保留当前已有的键
            for key, value in part_info.items():
                if key not in result:
                    result[key] = value
    else:
        content_type = msg.get_content_type()
        if content_type in ["text/plain", "text/html"]:
            content = msg.get_payload(decode=True)
            charset = guess_charset(msg)
            try:
                # 首先尝试使用指定的字符集
                if charset:
                    content = content.decode(charset)
                else:
                    # 如果没有指定字符集，尝试utf-8
                    content = content.decode("utf-8")
            except UnicodeDecodeError:
                try:
                    # 如果utf-8失败，尝试使用errors='replace'选项
                    if charset:
                        content = content.decode(charset, errors="replace")
                    else:
                        # 尝试其他常用编码
                        for encoding in [
                            "utf-8",
                            "latin-1",
                            "gbk",
                            "gb2312",
                            "gb18030",
                            "big5",
                        ]:
                            try:
                                content = content.decode(encoding)
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            # 如果所有编码都失败，使用latin-1（它可以解码任何字节序列）
                            content = content.decode("latin-1", errors="replace")
                except Exception as e:
                    content = f"[无法解码邮件内容: {str(e)}]"

            # 简单的提取文本内容，不进行过多的过滤
            if content_type == "text/html":
                try:
                    # 保存原始HTML内容并生成临时链接
                    if mail_id:
                        html_id = save_html_content(content, mail_id, username)
                        if html_id:
                            # 动态生成HTML预览链接，使用请求的host或默认域名
                            if request_host:
                                # 确保host包含协议
                                if not request_host.startswith(("http://", "https://")):
                                    base_url = f"https://{request_host}"
                                else:
                                    base_url = request_host
                            else:
                                # 如果没有提供request_host，尝试获取动态域名
                                try:
                                    domain = get_dynamic_domains()
                                    base_url = f"https://{domain}"
                                except ImportError:
                                    # 如果导入失败，使用默认域名
                                    base_url = "https://api.kedaya.xyz"

                            result["HtmlPreviewLink"] = (
                                f"{base_url}/api/email/preview/{html_id}"
                            )

                    soup = BeautifulSoup(content, "html.parser")

                    # 清理无用标签
                    for tag in ["style", "script"]:
                        for element in soup.find_all(tag):
                            element.decompose()

                    # 处理链接 - 为所有链接添加URL
                    for link in soup.find_all("a"):
                        href = link.get("href")
                        if href and "http" in href:
                            link_text = link.text.strip()
                            if link_text:
                                link.string = f"{link_text} [{href}]"
                            else:
                                link.string = f"[{href}]"

                    # 提取文本内容
                    text = soup.get_text(separator="\n", strip=True)

                    # 清理重复的空行
                    text = re.sub(r"\n{3,}", "\n\n", text)

                    # 将所有换行符替换为空格
                    text = text.replace("\n", " ")

                    result["Content"] = text.strip()
                except Exception:
                    # 如果HTML解析失败，直接返回原始内容
                    content = content.strip()
                    # 将所有换行符替换为空格
                    content = content.replace("\n", " ")
                    result["Content"] = content
            else:
                # 纯文本内容简单处理
                text = content.strip()
                # 将所有换行符替换为空格
                text = text.replace("\n", " ")
                result["Content"] = text
        else:
            result["Attachment"] = content_type

    for key in ["From", "To"]:
        if key in result:
            result[key] = result[key].strip()

    return result


# IMAP服务器连接缓存
class IMAPConnectionCache:
    """IMAP连接缓存管理器"""

    def __init__(self, cache_timeout: int = 300):  # 恢复到5分钟缓存，配合改进的健康检查
        self.cache: Dict[str, dict] = {}
        self.cache_timeout = cache_timeout
        self.lock = threading.RLock()
        self.max_connection_attempts = 3
        self.connection_retry_delay = {}  # 记录连接重试延迟

    def _is_connection_healthy(self, server: imaplib.IMAP4_SSL) -> bool:
        """检查连接是否健康"""
        try:
            # 检查连接状态
            if hasattr(server, "state") and server.state == "LOGOUT":
                return False

            # 执行NOOP命令测试连接，使用更短的超时
            try:
                response = server.noop()
                if response[0] != "OK":
                    return False
            except Exception as noop_e:
                # NOOP失败通常意味着连接已断开
                logger.debug(f"NOOP命令失败: {str(noop_e)}")
                return False

            # 简化收件箱检查，避免过于频繁的操作
            try:
                # 只检查当前选择的邮箱状态，不执行额外的select操作
                if hasattr(server, "state") and server.state == "SELECTED":
                    return True
                else:
                    # 如果没有选择邮箱，尝试选择一次
                    select_response = server.select("inbox")
                    return select_response[0] == "OK"
            except Exception as select_e:
                logger.debug(f"收件箱选择检查失败: {str(select_e)}")
                return False

        except Exception as e:
            logger.debug(f"连接健康检查异常: {str(e)}")
            return False

    def get_connection(self, email: str, password: str) -> Optional[imaplib.IMAP4_SSL]:
        """获取缓存的连接或创建新连接"""
        cache_key = f"{email}:{password}"

        with self.lock:
            # 检查缓存是否存在且有效
            if cache_key in self.cache:
                cached_data = self.cache[cache_key]
                server = cached_data["server"]
                cache_time = cached_data["time"]

                # 检查缓存是否过期
                if time.time() - cache_time < self.cache_timeout:
                    # 使用健康检查测试连接
                    if self._is_connection_healthy(server):
                        logger.info(f"使用缓存的IMAP连接: {email}")
                        return server
                    else:
                        logger.warning(f"缓存的IMAP连接健康检查失败: {email}")
                        # 连接失效，从缓存中移除
                        try:
                            if hasattr(server, "state") and server.state != "LOGOUT":
                                server.logout()
                        except:
                            pass
                        del self.cache[cache_key]
                else:
                    # 缓存过期，清理
                    logger.info(f"IMAP连接缓存过期: {email}")
                    try:
                        if hasattr(server, "state") and server.state != "LOGOUT":
                            server.logout()
                    except:
                        pass
                    del self.cache[cache_key]

            # 创建新连接
            max_retries = 3
            for attempt in range(max_retries):
                server = None
                try:
                    logger.info(
                        f"创建新的IMAP连接: {email} (尝试 {attempt + 1}/{max_retries})"
                    )

                    # 增加超时时间并使用更稳定的连接参数

                    # 创建SSL上下文，允许更宽松的SSL验证
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE

                    server = imaplib.IMAP4_SSL(
                        "mail.kedaya.xyz",
                        port=993,
                        timeout=60,  # 增加到60秒
                        ssl_context=ssl_context,
                    )

                    # 登录
                    login_response = server.login(email, password)
                    if login_response[0] != "OK":
                        raise Exception(f"登录失败: {login_response}")

                    # 选择收件箱并验证连接
                    response = server.select("inbox")
                    if response[0] != "OK":
                        raise Exception(f"选择收件箱失败: {response}")

                    # 测试连接是否正常工作
                    noop_response = server.noop()
                    if noop_response[0] != "OK":
                        raise Exception(f"连接测试失败: {noop_response}")

                    # 缓存连接
                    self.cache[cache_key] = {"server": server, "time": time.time()}
                    logger.info(f"IMAP连接创建成功并已缓存: {email}")
                    return server

                except Exception as e:
                    error_msg = str(e)
                    logger.error(
                        f"创建IMAP连接失败 (尝试 {attempt + 1}/{max_retries}): {email}, 错误: {error_msg}"
                    )

                    # 清理失败的连接
                    if server:
                        try:
                            server.logout()
                        except:
                            pass

                    if attempt < max_retries - 1:
                        # 根据错误类型和历史失败情况调整等待时间
                        if (
                            "timeout" in error_msg.lower()
                            or "socket" in error_msg.lower()
                            or "SSL" in error_msg
                            or "EOF" in error_msg
                        ):
                            # 网络/SSL问题使用更长的等待时间
                            base_wait = 10
                            wait_time = min(base_wait * (attempt + 1), 30)
                        else:
                            wait_time = 2**attempt  # 其他错误使用指数退避

                        # 记录重试延迟，避免过于频繁的重连
                        current_time = time.time()
                        if email in self.connection_retry_delay:
                            last_attempt = self.connection_retry_delay[email]
                            if current_time - last_attempt < 30:  # 30秒内不重复尝试
                                wait_time = max(
                                    wait_time, 30 - (current_time - last_attempt)
                                )

                        self.connection_retry_delay[email] = current_time
                        logger.info(f"等待 {wait_time:.1f} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"所有连接尝试均失败，无法连接到 {email}")
                        # 记录失败时间，避免立即重试
                        self.connection_retry_delay[email] = time.time()
                        raise Exception(f"无法建立IMAP连接到 {email}: {error_msg}")

    def cleanup_expired(self):
        """清理过期的连接"""
        with self.lock:
            current_time = time.time()
            expired_keys = []

            for cache_key, cached_data in self.cache.items():
                if current_time - cached_data["time"] >= self.cache_timeout:
                    expired_keys.append(cache_key)

            for key in expired_keys:
                try:
                    self.cache[key]["server"].logout()
                except:
                    pass
                del self.cache[key]
                logger.info(f"清理过期的IMAP连接: {key.split(':')[0]}")


# 全局IMAP连接缓存实例
imap_cache = IMAPConnectionCache()


# 优化邮件获取函数，添加IMAP连接缓存
def get_emails(
    account_email: str, num: str = "1", passwd: str = "", request_host: str = None
):
    """获取邮件

    Args:
        account_email: 邮箱账户
        num: 获取邮件数量
        passwd: 邮箱密码
        request_host: 请求的host信息，用于生成HTML预览链接
    """
    try:
        logger.info(f"邮件获取：账户 {account_email}, 密码 {passwd}, 数量 {num}")
        # 检查账户邮箱是否已包含域名
        if "@" in account_email:
            full_email = account_email  # 如果已经是完整邮箱地址，直接使用
            logger.info(f"使用完整邮箱地址: {full_email}")
        else:
            full_email = f"{account_email}@kedaya.xyz"  # 否则添加域名
            logger.info(f"添加域名后的邮箱地址: {full_email}")

        # 特殊账户处理
        if account_email == "<EMAIL>" or account_email == "qali":
            passwd = "<EMAIL>"
            full_email = "<EMAIL>"
            logger.info(f"特殊账户qali，使用固定密码和邮箱: {full_email}")
        elif account_email == "<EMAIL>" or account_email == "apil":
            passwd = "<EMAIL>"
            full_email = "<EMAIL>"
            logger.info(f"特殊账户apil，使用固定密码和邮箱: {full_email}")

        # 使用缓存的IMAP连接
        server = imap_cache.get_connection(full_email, passwd)

        # 重新搜索邮件（清理后）
        max_search_retries = 3
        for search_attempt in range(max_search_retries):
            try:
                # 确保连接状态正常
                if hasattr(server, "state") and server.state == "LOGOUT":
                    logger.warning(f"连接已处于LOGOUT状态，重新获取连接")
                    server = imap_cache.get_connection(full_email, passwd)

                # 确保选择了收件箱
                try:
                    select_response = server.select("inbox")
                    if select_response[0] != "OK":
                        logger.warning(f"选择收件箱失败: {select_response}")
                        raise Exception(f"选择收件箱失败: {select_response}")
                except Exception as select_e:
                    logger.error(f"选择收件箱时出错: {str(select_e)}")
                    if "illegal in state LOGOUT" in str(select_e):
                        # 连接已断开，重新获取连接
                        server = imap_cache.get_connection(full_email, passwd)
                        select_response = server.select("inbox")
                        if select_response[0] != "OK":
                            raise Exception(
                                f"重新连接后选择收件箱仍失败: {select_response}"
                            )
                    else:
                        raise select_e

                _, data = server.search(None, "ALL")
                if data and data[0]:
                    mail_ids = data[0].split()
                    logger.info(f"找到 {len(mail_ids)} 封邮件")
                    break
                else:
                    logger.warning(
                        f"搜索邮件返回空结果，尝试 {search_attempt + 1}/{max_search_retries}"
                    )
                    if search_attempt < max_search_retries - 1:
                        time.sleep(1)
                        # 重新选择收件箱
                        try:
                            server.select("inbox")
                        except Exception as reselect_e:
                            logger.warning(f"重新选择收件箱失败: {str(reselect_e)}")
                            # 如果重新选择失败，获取新连接
                            server = imap_cache.get_connection(full_email, passwd)
                    else:
                        mail_ids = []
            except Exception as e:
                logger.error(
                    f"搜索邮件失败，尝试 {search_attempt + 1}/{max_search_retries}: {str(e)}"
                )
                if search_attempt < max_search_retries - 1:
                    time.sleep(2)
                    # 尝试重新连接
                    try:
                        server = imap_cache.get_connection(full_email, passwd)
                    except Exception as reconnect_e:
                        logger.error(f"重新连接失败: {str(reconnect_e)}")
                        if search_attempt == max_search_retries - 1:
                            raise
                else:
                    raise

        if mail_ids:
            mail_ids_to_fetch = mail_ids[-int(num) :]
            messages = []

            for mail_id in mail_ids_to_fetch:
                max_fetch_retries = 2
                for fetch_attempt in range(max_fetch_retries):
                    try:
                        # 检查连接状态
                        if hasattr(server, "state") and server.state == "LOGOUT":
                            logger.warning(
                                f"获取邮件时连接已处于LOGOUT状态，重新获取连接"
                            )
                            server = imap_cache.get_connection(full_email, passwd)
                            server.select("inbox")

                        _, msg_data = server.fetch(mail_id, "(RFC822)")

                        # 检查返回的数据格式
                        if not msg_data or not msg_data[0] or len(msg_data[0]) < 2:
                            logger.error(
                                f"邮件获取：邮件ID {mail_id} 返回数据格式异常: {msg_data}"
                            )
                            break

                        msg_content = msg_data[0][1]

                        # 确保msg_content是bytes类型
                        if not isinstance(msg_content, bytes):
                            logger.error(
                                f"邮件获取：邮件ID {mail_id} 内容不是bytes类型: {type(msg_content)}"
                            )
                            break

                        # 尝试不同的解码方式来处理原始邮件内容
                        try:
                            # 使用更健壮的邮件解析方式
                            from email.parser import BytesParser

                            # 首先尝试使用BytesParser直接解析bytes
                            msg = BytesParser().parsebytes(msg_content)
                        except Exception as bytes_parse_error:
                            try:
                                # 如果BytesParser失败，尝试使用latin-1解码
                                msg = Parser().parsestr(msg_content.decode("latin-1"))
                            except Exception as latin_error:
                                try:
                                    # 最后尝试用UTF-8但使用replace模式
                                    msg = Parser().parsestr(
                                        msg_content.decode("utf-8", errors="replace")
                                    )
                                except Exception as utf_error:
                                    logger.error(
                                        f"邮件解析：解析邮件ID {mail_id} 时出错: BytesParser={str(bytes_parse_error)}, Latin1={str(latin_error)}, UTF8={str(utf_error)}"
                                    )
                                    break

                        # 将mail_id转换为字符串并传递给extract_info，同时传递用户名和请求host
                        msg_info = extract_info(msg, str(mail_id), passwd, request_host)
                        if msg_info:
                            messages.append(msg_info)
                        break  # 成功处理，跳出重试循环

                    except Exception as e:
                        error_msg = str(e)
                        logger.error(
                            f"邮件获取：获取邮件ID {mail_id} 时出错 (尝试 {fetch_attempt + 1}/{max_fetch_retries}): {error_msg}"
                        )

                        # 检查是否是连接状态错误
                        if (
                            "illegal in state LOGOUT" in error_msg
                            or "unexpected response" in error_msg
                            or "socket error" in error_msg
                        ):
                            logger.warning(f"检测到连接问题，尝试重新建立连接")
                            try:
                                # 重新获取连接
                                server = imap_cache.get_connection(full_email, passwd)
                                server.select("inbox")
                                # 如果重新连接成功，继续重试
                                if fetch_attempt < max_fetch_retries - 1:
                                    time.sleep(1)
                                    continue
                            except Exception as reconnect_e:
                                logger.error(f"重新连接失败: {str(reconnect_e)}")

                        if fetch_attempt < max_fetch_retries - 1:
                            time.sleep(1)
                        else:
                            # 最后一次尝试失败，跳过这封邮件
                            logger.warning(f"跳过邮件ID {mail_id}，无法获取内容")
                            continue
            messages.sort(key=lambda x: x["Time"] if "Time" in x else 0, reverse=True)
            # 注意：不要在这里logout，因为连接已被缓存
            logger.info(f"邮件获取完成，共 {len(messages)} 封邮件")
            logger.debug(f"邮件内容：{messages}")

            # 定期清理过期的IMAP连接（每10次请求清理一次）
            if hasattr(get_emails, "_cleanup_counter"):
                get_emails._cleanup_counter += 1
            else:
                get_emails._cleanup_counter = 1

            if get_emails._cleanup_counter >= 10:
                try:
                    imap_cache.cleanup_expired()
                    get_emails._cleanup_counter = 0
                except Exception as cleanup_error:
                    logger.warning(f"清理IMAP连接缓存时出错: {str(cleanup_error)}")

            return messages
        else:
            # 注意：不要在这里logout，因为连接已被缓存
            logger.info("邮箱中没有邮件")
            return {"msg": "没有邮件可提取"}
    except imaplib.IMAP4.error as e:
        logger.error(f"邮件服务：IMAP错误: {str(e)}")
        return {"msg": "未注册"}
    except Exception as e:
        logger.error(f"邮件服务：获取邮件时出错: {str(e)}")
        return {"msg": str(e)}


# 使用线程池异步获取邮件
async def get_emails_async(
    account_email: str, num: str = "1", passwd: str = "", request_host: str = None
):
    """异步获取邮件内容，使用线程池执行IO操作"""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(
        email_thread_pool, lambda: get_emails(account_email, num, passwd, request_host)
    )


def extract_verification_code(email_content):
    """从邮件内容中提取验证码"""

    # 匹配中文冒号
    match = re.search(r"验证码[是为]\s*[：:]\s*([0-9]{6})", email_content)
    if match:
        return match.group(1)

    # 如果上面的模式没匹配到，尝试匹配连续的6位数字
    match = re.search(r"[^0-9]([0-9]{6})[^0-9]", email_content)
    if match:
        return match.group(1)

    # 尝试匹配任何6位连续数字
    match = re.search(r"(\d{6})", email_content)
    if match:
        return match.group(1)

    # 记录无法提取验证码的情况
    logger.warning(f"验证码提取：无法从以下内容中提取验证码: {email_content}")
    return None


def create_email_account(email, username):
    """创建邮箱账户"""
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry

    # 配置重试策略
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1,
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    url = f"http://189.1.234.127:7777/email/create?email={email}&passwd={username}"

    try:
        logger.info(f"尝试创建邮箱账户: {email}")
        response = session.get(url, timeout=10)

        if response.status_code == 200:
            result = response.json()
            logger.info(f"邮箱创建API响应: {result}")
            return {
                "message": result.get("message", "邮箱账号创建成功"),
                "email": email,
                "username": username,
            }
        else:
            logger.error(f"邮箱创建API返回错误状态码: {response.status_code}")
            return {
                "message": f"邮箱创建失败: HTTP {response.status_code}",
                "email": email,
                "username": username,
            }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"数据库会话错误: {str(e)}")
        logger.error(f"操作错误: {str(e)}")
        # 返回一个错误但不会导致系统崩溃的响应
        return {
            "message": "邮箱服务暂时不可用，但账户已记录",
            "email": email,
            "username": username,
        }
    except requests.exceptions.Timeout as e:
        logger.error(f"邮箱创建请求超时: {str(e)}")
        return {
            "message": "邮箱创建请求超时，请稍后重试",
            "email": email,
            "username": username,
        }
    except Exception as e:
        logger.error(f"邮箱创建发生未知错误: {str(e)}")
        return {
            "message": f"邮箱创建失败: {str(e)}",
            "email": email,
            "username": username,
        }
    finally:
        session.close()


def create_html_email_template(content, email_type="general"):
    """创建HTML邮件模板

    参数:
    content: 邮件正文内容
    email_type: 邮件类型 (verification, reset_password, general)
    """

    # 根据邮件类型设置不同的图标和颜色
    if email_type == "verification":
        primary_color = "#4361EE"
        title = "邮箱验证"
    elif email_type == "reset_password":
        primary_color = "#FF6B6B"
        title = "密码重置"
    else:
        primary_color = "#4361EE"
        title = "系统通知"

    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>可达鸭API - {title}</title>
        <style>
            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}
            
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f7fa;
            }}
            
            .email-container {{
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }}
            
            .header {{
                background: linear-gradient(135deg, {primary_color} 0%, #667eea 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }}
            
            .header h1 {{
                font-size: 28px;
                font-weight: 600;
                margin-bottom: 8px;
            }}
            
            .header .subtitle {{
                font-size: 16px;
                opacity: 0.9;
            }}
            
            .content {{
                padding: 40px 30px;
            }}
            
            .message {{
                font-size: 16px;
                line-height: 1.8;
                color: #555;
                margin-bottom: 30px;
                text-align: center;
            }}
            
            .verification-code {{
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: 8px;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin: 30px 0;
                font-family: 'Courier New', monospace;
                box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
            }}
            
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
                font-size: 14px;
            }}
            
            .footer {{
                background-color: #f8f9fa;
                padding: 30px 20px;
                text-align: center;
                border-top: 1px solid #e9ecef;
            }}
            
            .footer .brand {{
                font-size: 20px;
                font-weight: 600;
                color: {primary_color};
                margin-bottom: 10px;
            }}
            
            .footer .website {{
                margin: 15px 0;
            }}
            
            .footer .website a {{
                color: {primary_color};
                text-decoration: none;
                font-weight: 500;
                padding: 8px 16px;
                border: 2px solid {primary_color};
                border-radius: 25px;
                transition: all 0.3s ease;
                display: inline-block;
            }}
            
            .footer .website a:hover {{
                background-color: {primary_color};
                color: white;
            }}
            
            .footer .contact {{
                font-size: 14px;
                color: #666;
                margin-top: 20px;
            }}
            
            .footer .social {{
                margin: 20px 0;
            }}
            
            .footer .social a {{
                display: inline-block;
                margin: 0 10px;
                color: #666;
                text-decoration: none;
                font-size: 14px;
            }}
            
            .divider {{
                height: 1px;
                background: linear-gradient(90deg, transparent, #ddd, transparent);
                margin: 30px 0;
            }}
            
            @media (max-width: 600px) {{
                .email-container {{
                    margin: 10px;
                    border-radius: 8px;
                }}
                
                .header {{
                    padding: 20px 15px;
                }}
                
                .header h1 {{
                    font-size: 24px;
                }}
                
                .content {{
                    padding: 30px 20px;
                }}
                
                .verification-code {{
                    font-size: 28px;
                    letter-spacing: 6px;
                    padding: 15px;
                }}
                
                .footer {{
                    padding: 20px 15px;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                <h1>可达鸭API</h1>
                <div class="subtitle">专业的API服务平台</div>
            </div>
            
            <div class="content">
                <div class="message">
                    {content}
                </div>
            </div>
            
            <div class="footer">
                <div class="brand">可达鸭API</div>
                
                <div class="website">
                    <a href="https://api.kedaya.xyz" target="_blank">访问官网</a>
                </div>
                
                <div class="social">
                    <a href="https://api.kedaya.xyz" target="_blank">官方网站</a>
                    <a href="mailto:<EMAIL>">技术支持</a>
                </div>
                
                <div class="contact">
                    <p>如有疑问，请联系我们的技术支持团队</p>
                    <p>邮箱：<EMAIL></p>
                    <p>© 2025 可达鸭API. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return html_template


def send_email(from_email, to_email, subject, content, username):
    """发送邮件

    参数:
    from_email: 发件人邮箱前缀
    to_email: 收件人完整邮箱地址
    subject: 邮件主题
    content: 邮件内容
    username: 用户名(作为密码)
    """
    try:
        logger.info(f"邮件发送：从 {from_email} 发送到 {to_email}")

        # 构建完整邮箱地址
        full_email = f"{from_email}@kedaya.xyz"

        # 创建邮件对象
        msg = MIMEMultipart("alternative")
        # 设置发件人显示名称为"可达鸭API"
        msg["From"] = formataddr(("可达鸭API", full_email))
        msg["To"] = to_email
        msg["Subject"] = subject

        # 判断邮件类型
        email_type = "general"
        if "验证码" in subject:
            if "注册" in subject:
                email_type = "verification"
            elif "重置" in subject or "密码" in subject:
                email_type = "reset_password"

        # 处理验证码显示
        html_content = content
        if "验证码" in content:
            # 提取验证码
            import re

            code_match = re.search(r"(\d{6})", content)
            if code_match:
                verification_code = code_match.group(1)
                # 替换内容，添加验证码样式
                html_content = content.replace(
                    verification_code,
                    f'</div><div class="verification-code">{verification_code}</div><div class="message">',
                )
                # 清理多余的div标签
                html_content = html_content.replace('<div class="message"></div>', "")

        # 创建HTML版本
        html_body = create_html_email_template(html_content, email_type)

        # 创建纯文本版本（作为备用）
        text_body = (
            content
            + "\n\n"
            + "=" * 50
            + "\n"
            + "可达鸭API - 专业的API服务平台\n"
            + "官网：https://api.kedaya.xyz\n"
            + "技术支持：<EMAIL>\n"
            + "© 2024 可达鸭API. 保留所有权利."
        )

        # 添加邮件内容
        msg.attach(MIMEText(text_body, "plain", "utf-8"))
        msg.attach(MIMEText(html_body, "html", "utf-8"))

        # 连接SMTP服务器
        server = smtplib.SMTP_SSL("mail.kedaya.xyz", 465, timeout=10)
        server.login(full_email, username)

        # 发送邮件
        server.send_message(msg)
        server.quit()

        logger.info(f"邮件发送成功：{subject} -> {to_email}")
        return {"status": True, "message": "邮件发送成功"}
    except Exception as e:
        logger.error(f"邮件发送失败: {str(e)}")
        return {"status": False, "message": f"邮件发送失败: 此邮箱已被他人使用"}


# 使用线程池异步发送邮件
async def send_email_async(from_email, to_email, subject, content, username):
    """异步发送邮件，使用线程池执行IO操作"""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(
        email_thread_pool,
        lambda: send_email(from_email, to_email, subject, content, username),
    )


# 为预览HTML内容提供的函数
def get_html_preview(html_id):
    """根据ID获取HTML预览内容

    参数:
    html_id: HTML内容的唯一标识符

    返回:
    HTML内容或错误信息
    """
    try:
        # 从数据库获取HTML内容
        with get_db() as db:
            result = db.execute(
                temp_html_storage.select().where(temp_html_storage.c.id == html_id)
            ).fetchone()

            if not result:
                return {"status": False, "message": "预览链接不存在或已过期"}

            # 检查是否过期
            current_time = int(time.time())
            if result.expires_at < current_time:
                # 清理过期记录
                db.execute(
                    temp_html_storage.delete().where(temp_html_storage.c.id == html_id)
                )
                db.commit()
                return {"status": False, "message": "预览链接已过期"}

            # 返回HTML内容
            return {"status": True, "html": result.html_content}

    except Exception as e:
        logger.error(f"获取HTML预览时出错: {str(e)}")
        return {"status": False, "message": f"获取HTML预览失败: {str(e)}"}


# 清理过期的HTML记录
def cleanup_expired_html_records():
    """清理过期的临时HTML记录"""
    try:
        current_time = int(time.time())
        with get_db() as db:
            # 删除所有过期的记录
            result = db.execute(
                temp_html_storage.delete().where(
                    temp_html_storage.c.expires_at < current_time
                )
            )
            db.commit()

            records_deleted = result.rowcount
            if records_deleted > 0:
                logger.info(f"已清理 {records_deleted} 条过期HTML预览记录")

    except Exception as e:
        logger.error(f"清理过期HTML记录出错: {str(e)}")
